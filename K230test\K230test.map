Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to uart_app.o(i.Rec_Data) for Rec_Data
    main.o(i.main) refers to uart_app.o(.data) for rec_data
    main.o(i.main) refers to usart.o(.bss) for huart1
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.OCRrec_Actions) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_app.o(i.OCRrec_Actions) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    uart_app.o(i.OCRrec_Actions) refers to _printf_str.o(.text) for _printf_str
    uart_app.o(i.OCRrec_Actions) refers to strcmpv7m.o(.text) for strcmp
    uart_app.o(i.OCRrec_Actions) refers to noretval__2sprintf.o(.text) for __2sprintf
    uart_app.o(i.OCRrec_Actions) refers to strlen.o(.text) for strlen
    uart_app.o(i.OCRrec_Actions) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_app.o(i.OCRrec_Actions) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.OCRrec_Actions) refers to usart.o(.bss) for huart2
    uart_app.o(i.Rec_Data) refers to yb_protocol.o(i.Pto_Data_Receive) for Pto_Data_Receive
    uart_app.o(i.Rec_Data) refers to yb_protocol.o(i.Pto_Loop) for Pto_Loop
    uart_app.o(i.Rec_Data) refers to rt_memclr.o(.text) for __aeabi_memclr
    uart_app.o(i.Rec_Data) refers to uart_app.o(.data) for .data
    uart_app.o(i.Rec_Data) refers to uart_app.o(.bss) for .bss
    yb_protocol.o(i.Get_LOST_Flag) refers to yb_protocol.o(.data) for .data
    yb_protocol.o(i.HandleMultiColorRec) refers to strcmpv7m.o(.text) for strcmp
    yb_protocol.o(i.HandleMultiColorRec) refers to noretval__2sprintf.o(.text) for __2sprintf
    yb_protocol.o(i.HandleMultiColorRec) refers to strlen.o(.text) for strlen
    yb_protocol.o(i.HandleMultiColorRec) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    yb_protocol.o(i.HandleMultiColorRec) refers to uart_app.o(.bss) for send_buff
    yb_protocol.o(i.HandleMultiColorRec) refers to usart.o(.bss) for huart2
    yb_protocol.o(i.HandleOCRRec) refers to uart_app.o(i.OCRrec_Actions) for OCRrec_Actions
    yb_protocol.o(i.ParseCommonFields) refers to yb_protocol.o(i.Pto_Char_To_Int) for Pto_Char_To_Int
    yb_protocol.o(i.ParseCommonFields) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    yb_protocol.o(i.ParseCommonFields) refers to yb_protocol.o(.constdata) for .constdata
    yb_protocol.o(i.Pto_Char_To_Int) refers to atoi.o(.text) for atoi
    yb_protocol.o(i.Pto_Clear_CMD_Flag) refers to yb_protocol.o(.bss) for .bss
    yb_protocol.o(i.Pto_Clear_CMD_Flag) refers to yb_protocol.o(.data) for .data
    yb_protocol.o(i.Pto_Data_Parse) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    yb_protocol.o(i.Pto_Data_Parse) refers to yb_protocol.o(i.Pto_Char_To_Int) for Pto_Char_To_Int
    yb_protocol.o(i.Pto_Data_Parse) refers to yb_protocol.o(i.ParseCommonFields) for ParseCommonFields
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(i.Pto_Clear_CMD_Flag) for Pto_Clear_CMD_Flag
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(.data) for .data
    yb_protocol.o(i.Pto_Data_Receive) refers to yb_protocol.o(.bss) for .bss
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Pto_Data_Parse) for Pto_Data_Parse
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Pto_Clear_CMD_Flag) for Pto_Clear_CMD_Flag
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(i.Get_LOST_Flag) for Get_LOST_Flag
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(.data) for .data
    yb_protocol.o(i.Pto_Loop) refers to yb_protocol.o(.bss) for .bss
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for color_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleColor) for HandleColor
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for barcode_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleBarcode) for HandleBarcode
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for qrcode_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleQRCode) for HandleQRCode
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for apriltag_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleAprilTag) for HandleAprilTag
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for dmcode_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleDMCode) for HandleDMCode
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for facedetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFaceDetect) for HandleFaceDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for eyegaze_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleEyeGaze) for HandleEyeGaze
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for facerecog_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFaceRecognition) for HandleFaceRecognition
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for persondetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandlePersonDetect) for HandlePersonDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for falldown_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleFallDown) for HandleFallDown
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for handdetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleHandDetect) for HandleHandDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for handgesture_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleHandGesture) for HandleHandGesture
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for ocrrec_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleOCRRec) for HandleOCRRec
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for objectdetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleObjectDetect) for HandleObjectDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for nanotracker_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleNanoTracker) for HandleNanoTracker
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for selflearning_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleSelfLearning) for HandleSelfLearning
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for licencerec_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleLicenceRec) for HandleLicenceRec
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for licencedetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleLicenceDetect) for HandleLicenceDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for garbagedetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleGarbageDetect) for HandleGarbageDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for guidedetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleGuideDetect) for HandleGuideDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for obstacledetect_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleObstacleDetect) for HandleObstacleDetect
    yb_protocol.o(.constdata) refers to yb_protocol.o(.constdata) for multicolorrec_fields
    yb_protocol.o(.constdata) refers to yb_protocol.o(i.HandleMultiColorRec) for HandleMultiColorRec
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (88 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (416 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.bss), (20 bytes).
    Removing uart_app.o(.bss), (30 bytes).
    Removing yb_protocol.o(.rev16_text), (4 bytes).
    Removing yb_protocol.o(.revsh_text), (4 bytes).
    Removing yb_protocol.o(.rrx_text), (6 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).
    Removing yb_protocol.o(.data), (4 bytes).

265 unused section(s) (total 13898 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\K230\uart_app.c                       0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\K230\yb_protocol.c                    0x00000000   Number         0  yb_protocol.o ABSOLUTE
    ..\\K230\\uart_app.c                     0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\K230\\yb_protocol.c                  0x00000000   Number         0  yb_protocol.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000014  0x080001fc   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000202   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000206   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000208   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800020c   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000212   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800021e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000220   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000222   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000222   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000224   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000224   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000224   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800022a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800022a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800022e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800022e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000236   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000238   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000238   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800023c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000244   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000244   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000284   Section      238  lludivv7m.o(.text)
    .text                                    0x08000374   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x0800039c   Section        0  __printf.o(.text)
    .text                                    0x08000404   Section        0  _printf_str.o(.text)
    .text                                    0x08000456   Section        0  atoi.o(.text)
    .text                                    0x08000470   Section        0  strlen.o(.text)
    .text                                    0x080004ae   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000538   Section       68  rt_memclr.o(.text)
    .text                                    0x0800057c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080005cc   Section      128  strcmpv7m.o(.text)
    .text                                    0x0800064c   Section        0  heapauxi.o(.text)
    .text                                    0x08000654   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800065c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800065d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800068c   Section        0  _sputc.o(.text)
    .text                                    0x08000696   Section        0  _printf_char.o(.text)
    .text                                    0x080006c2   Section        0  strtol.o(.text)
    .text                                    0x08000732   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000798   Section        8  libspace.o(.text)
    .text                                    0x080007a0   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080007b0   Section        0  _strtoul.o(.text)
    .text                                    0x0800084e   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000898   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080008a0   Section        0  _chval.o(.text)
    .text                                    0x080008bc   Section        0  exit.o(.text)
    .text                                    0x080008d0   Section        0  sys_exit.o(.text)
    .text                                    0x080008dc   Section        2  use_no_semi.o(.text)
    .text                                    0x080008de   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x080008de   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080008e0   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080008e2   Section        0  main.o(i.Error_Handler)
    i.Get_LOST_Flag                          0x080008e8   Section        0  yb_protocol.o(i.Get_LOST_Flag)
    i.HAL_DMA_Abort                          0x08000910   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080009a2   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_GPIO_Init                          0x080009c8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GetTick                            0x08000bb8   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000bc4   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000bd4   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000c08   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000c48   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000c78   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000c94   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000cd4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000cf8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08000e2c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000e4c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000e6c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000ed0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800123c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_RxEventCallback             0x08001264   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001266   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08001268   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080014e8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x0800154c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08001614   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08001630   Section        0  uart_app.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08001664   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08001704   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HandleAprilTag                         0x08001706   Section        0  yb_protocol.o(i.HandleAprilTag)
    i.HandleBarcode                          0x08001708   Section        0  yb_protocol.o(i.HandleBarcode)
    i.HandleColor                            0x0800170a   Section        0  yb_protocol.o(i.HandleColor)
    i.HandleDMCode                           0x0800170c   Section        0  yb_protocol.o(i.HandleDMCode)
    i.HandleEyeGaze                          0x0800170e   Section        0  yb_protocol.o(i.HandleEyeGaze)
    i.HandleFaceDetect                       0x08001710   Section        0  yb_protocol.o(i.HandleFaceDetect)
    i.HandleFaceRecognition                  0x08001712   Section        0  yb_protocol.o(i.HandleFaceRecognition)
    i.HandleFallDown                         0x08001714   Section        0  yb_protocol.o(i.HandleFallDown)
    i.HandleGarbageDetect                    0x08001716   Section        0  yb_protocol.o(i.HandleGarbageDetect)
    i.HandleGuideDetect                      0x08001718   Section        0  yb_protocol.o(i.HandleGuideDetect)
    i.HandleHandDetect                       0x0800171a   Section        0  yb_protocol.o(i.HandleHandDetect)
    i.HandleHandGesture                      0x0800171c   Section        0  yb_protocol.o(i.HandleHandGesture)
    i.HandleLicenceDetect                    0x0800171e   Section        0  yb_protocol.o(i.HandleLicenceDetect)
    i.HandleLicenceRec                       0x08001720   Section        0  yb_protocol.o(i.HandleLicenceRec)
    i.HandleMultiColorRec                    0x08001724   Section        0  yb_protocol.o(i.HandleMultiColorRec)
    i.HandleNanoTracker                      0x08001788   Section        0  yb_protocol.o(i.HandleNanoTracker)
    i.HandleOCRRec                           0x0800178a   Section        0  yb_protocol.o(i.HandleOCRRec)
    i.HandleObjectDetect                     0x08001790   Section        0  yb_protocol.o(i.HandleObjectDetect)
    i.HandleObstacleDetect                   0x08001792   Section        0  yb_protocol.o(i.HandleObstacleDetect)
    i.HandlePersonDetect                     0x08001794   Section        0  yb_protocol.o(i.HandlePersonDetect)
    i.HandleQRCode                           0x08001796   Section        0  yb_protocol.o(i.HandleQRCode)
    i.HandleSelfLearning                     0x08001798   Section        0  yb_protocol.o(i.HandleSelfLearning)
    i.HardFault_Handler                      0x0800179a   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_GPIO_Init                           0x0800179c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x080017c8   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08001800   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x08001838   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800183a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OCRrec_Actions                         0x0800183c   Section        0  uart_app.o(i.OCRrec_Actions)
    i.ParseCommonFields                      0x08001914   Section        0  yb_protocol.o(i.ParseCommonFields)
    i.PendSV_Handler                         0x080019a8   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Pto_Char_To_Int                        0x080019aa   Section        0  yb_protocol.o(i.Pto_Char_To_Int)
    i.Pto_Clear_CMD_Flag                     0x080019b0   Section        0  yb_protocol.o(i.Pto_Clear_CMD_Flag)
    i.Pto_Data_Parse                         0x080019d0   Section        0  yb_protocol.o(i.Pto_Data_Parse)
    i.Pto_Data_Receive                       0x08001a60   Section        0  yb_protocol.o(i.Pto_Data_Receive)
    i.Pto_Loop                               0x08001ab4   Section        0  yb_protocol.o(i.Pto_Loop)
    i.Rec_Data                               0x08001aec   Section        0  uart_app.o(i.Rec_Data)
    i.SVC_Handler                            0x08001b28   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001b2a   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001b30   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001bc4   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x08001bd4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08001bd5   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08001be2   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08001be3   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08001c30   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08001c31   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08001cf4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08001cf5   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08001e00   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08001e36   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08001e37   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08001ea8   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08001eb4   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x08001ec0   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08001ec2   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08001ec3   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x08001ee4   Section        0  main.o(i.main)
    locale$$code                             0x08001f14   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fpinit                             0x08001f40   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08001f40   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08001f4a   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08001f5a   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08001f62   Section        8  yb_protocol.o(.constdata)
    color_fields                             0x08001f62   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001f6a   Section       10  yb_protocol.o(.constdata)
    barcode_fields                           0x08001f6a   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08001f74   Section       10  yb_protocol.o(.constdata)
    qrcode_fields                            0x08001f74   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08001f7e   Section       12  yb_protocol.o(.constdata)
    apriltag_fields                          0x08001f7e   Data          12  yb_protocol.o(.constdata)
    .constdata                               0x08001f8a   Section       12  yb_protocol.o(.constdata)
    dmcode_fields                            0x08001f8a   Data          12  yb_protocol.o(.constdata)
    .constdata                               0x08001f96   Section        8  yb_protocol.o(.constdata)
    facedetect_fields                        0x08001f96   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001f9e   Section        8  yb_protocol.o(.constdata)
    eyegaze_fields                           0x08001f9e   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001fa6   Section       12  yb_protocol.o(.constdata)
    facerecog_fields                         0x08001fa6   Data          12  yb_protocol.o(.constdata)
    .constdata                               0x08001fb2   Section        8  yb_protocol.o(.constdata)
    persondetect_fields                      0x08001fb2   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001fba   Section       12  yb_protocol.o(.constdata)
    falldown_fields                          0x08001fba   Data          12  yb_protocol.o(.constdata)
    .constdata                               0x08001fc6   Section        8  yb_protocol.o(.constdata)
    handdetect_fields                        0x08001fc6   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001fce   Section        2  yb_protocol.o(.constdata)
    handgesture_fields                       0x08001fce   Data           2  yb_protocol.o(.constdata)
    .constdata                               0x08001fd0   Section        2  yb_protocol.o(.constdata)
    ocrrec_fields                            0x08001fd0   Data           2  yb_protocol.o(.constdata)
    .constdata                               0x08001fd2   Section       10  yb_protocol.o(.constdata)
    objectdetect_fields                      0x08001fd2   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08001fdc   Section        8  yb_protocol.o(.constdata)
    nanotracker_fields                       0x08001fdc   Data           8  yb_protocol.o(.constdata)
    .constdata                               0x08001fe4   Section        4  yb_protocol.o(.constdata)
    selflearning_fields                      0x08001fe4   Data           4  yb_protocol.o(.constdata)
    .constdata                               0x08001fe8   Section        2  yb_protocol.o(.constdata)
    licencerec_fields                        0x08001fe8   Data           2  yb_protocol.o(.constdata)
    .constdata                               0x08001fea   Section       16  yb_protocol.o(.constdata)
    licencedetect_fields                     0x08001fea   Data          16  yb_protocol.o(.constdata)
    .constdata                               0x08001ffa   Section       10  yb_protocol.o(.constdata)
    garbagedetect_fields                     0x08001ffa   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08002004   Section       10  yb_protocol.o(.constdata)
    guidedetect_fields                       0x08002004   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x0800200e   Section       10  yb_protocol.o(.constdata)
    obstacledetect_fields                    0x0800200e   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08002018   Section       10  yb_protocol.o(.constdata)
    multicolorrec_fields                     0x08002018   Data          10  yb_protocol.o(.constdata)
    .constdata                               0x08002024   Section      352  yb_protocol.o(.constdata)
    locale$$data                             0x080021a4   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x080021a8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080021b0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080022b4   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section        3  uart_app.o(.data)
    .data                                    0x20000014   Section       16  yb_protocol.o(.data)
    .bss                                     0x20000024   Section      144  usart.o(.bss)
    .bss                                     0x200000b4   Section      264  uart_app.o(.bss)
    .bss                                     0x200001bc   Section       50  yb_protocol.o(.bss)
    .bss                                     0x200001f0   Section       96  libspace.o(.bss)
    HEAP                                     0x20000250   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000250   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000450   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000450   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000850   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_s                                0x080001fd   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000203   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000207   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_alloca_1                   0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_ctype_1                 0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000221   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000223   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000225   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000225   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000225   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800022b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800022b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800022f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800022f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000237   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000239   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000239   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800023d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000245   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800025f   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000261   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000285   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000285   Thumb Code   238  lludivv7m.o(.text)
    __2sprintf                               0x08000375   Thumb Code    34  noretval__2sprintf.o(.text)
    __printf                                 0x0800039d   Thumb Code   104  __printf.o(.text)
    _printf_str                              0x08000405   Thumb Code    82  _printf_str.o(.text)
    atoi                                     0x08000457   Thumb Code    26  atoi.o(.text)
    strlen                                   0x08000471   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x080004af   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080004af   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000515   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr                           0x08000539   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000539   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800053d   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800057d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800057d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800057d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000581   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080005cd   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x0800064d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800064f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000651   Thumb Code     2  heapauxi.o(.text)
    __aeabi_errno_addr                       0x08000655   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000655   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000655   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x08000667   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x0800068d   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000697   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080006ab   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080006bb   Thumb Code     8  _printf_char.o(.text)
    strtol                                   0x080006c3   Thumb Code   112  strtol.o(.text)
    __aeabi_memcpy4                          0x08000733   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000733   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000733   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800077b   Thumb Code     0  rt_memcpy_w.o(.text)
    __user_libspace                          0x08000799   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000799   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000799   Thumb Code     0  libspace.o(.text)
    __rt_ctype_table                         0x080007a1   Thumb Code    16  rt_ctype_table.o(.text)
    _strtoul                                 0x080007b1   Thumb Code   158  _strtoul.o(.text)
    __user_setup_stackheap                   0x0800084f   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x08000899   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _chval                                   0x080008a1   Thumb Code    28  _chval.o(.text)
    exit                                     0x080008bd   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x080008d1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080008dd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080008dd   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x080008df   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x080008df   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x080008e1   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080008e3   Thumb Code     4  main.o(i.Error_Handler)
    Get_LOST_Flag                            0x080008e9   Thumb Code    34  yb_protocol.o(i.Get_LOST_Flag)
    HAL_DMA_Abort                            0x08000911   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080009a3   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_GPIO_Init                            0x080009c9   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GetTick                              0x08000bb9   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000bc5   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000bd5   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000c09   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000c49   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000c79   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000c95   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000cd5   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000cf9   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08000e2d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000e4d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000e6d   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000ed1   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800123d   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_RxEventCallback               0x08001265   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001267   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001269   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080014e9   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x0800154d   Thumb Code   182  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08001615   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08001631   Thumb Code    40  uart_app.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08001665   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08001705   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HandleAprilTag                           0x08001707   Thumb Code     2  yb_protocol.o(i.HandleAprilTag)
    HandleBarcode                            0x08001709   Thumb Code     2  yb_protocol.o(i.HandleBarcode)
    HandleColor                              0x0800170b   Thumb Code     2  yb_protocol.o(i.HandleColor)
    HandleDMCode                             0x0800170d   Thumb Code     2  yb_protocol.o(i.HandleDMCode)
    HandleEyeGaze                            0x0800170f   Thumb Code     2  yb_protocol.o(i.HandleEyeGaze)
    HandleFaceDetect                         0x08001711   Thumb Code     2  yb_protocol.o(i.HandleFaceDetect)
    HandleFaceRecognition                    0x08001713   Thumb Code     2  yb_protocol.o(i.HandleFaceRecognition)
    HandleFallDown                           0x08001715   Thumb Code     2  yb_protocol.o(i.HandleFallDown)
    HandleGarbageDetect                      0x08001717   Thumb Code     2  yb_protocol.o(i.HandleGarbageDetect)
    HandleGuideDetect                        0x08001719   Thumb Code     2  yb_protocol.o(i.HandleGuideDetect)
    HandleHandDetect                         0x0800171b   Thumb Code     2  yb_protocol.o(i.HandleHandDetect)
    HandleHandGesture                        0x0800171d   Thumb Code     2  yb_protocol.o(i.HandleHandGesture)
    HandleLicenceDetect                      0x0800171f   Thumb Code     2  yb_protocol.o(i.HandleLicenceDetect)
    HandleLicenceRec                         0x08001721   Thumb Code     2  yb_protocol.o(i.HandleLicenceRec)
    HandleMultiColorRec                      0x08001725   Thumb Code    70  yb_protocol.o(i.HandleMultiColorRec)
    HandleNanoTracker                        0x08001789   Thumb Code     2  yb_protocol.o(i.HandleNanoTracker)
    HandleOCRRec                             0x0800178b   Thumb Code     6  yb_protocol.o(i.HandleOCRRec)
    HandleObjectDetect                       0x08001791   Thumb Code     2  yb_protocol.o(i.HandleObjectDetect)
    HandleObstacleDetect                     0x08001793   Thumb Code     2  yb_protocol.o(i.HandleObstacleDetect)
    HandlePersonDetect                       0x08001795   Thumb Code     2  yb_protocol.o(i.HandlePersonDetect)
    HandleQRCode                             0x08001797   Thumb Code     2  yb_protocol.o(i.HandleQRCode)
    HandleSelfLearning                       0x08001799   Thumb Code     2  yb_protocol.o(i.HandleSelfLearning)
    HardFault_Handler                        0x0800179b   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_GPIO_Init                             0x0800179d   Thumb Code    38  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x080017c9   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08001801   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x08001839   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800183b   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OCRrec_Actions                           0x0800183d   Thumb Code   104  uart_app.o(i.OCRrec_Actions)
    ParseCommonFields                        0x08001915   Thumb Code   144  yb_protocol.o(i.ParseCommonFields)
    PendSV_Handler                           0x080019a9   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Pto_Char_To_Int                          0x080019ab   Thumb Code     4  yb_protocol.o(i.Pto_Char_To_Int)
    Pto_Clear_CMD_Flag                       0x080019b1   Thumb Code    24  yb_protocol.o(i.Pto_Clear_CMD_Flag)
    Pto_Data_Parse                           0x080019d1   Thumb Code   142  yb_protocol.o(i.Pto_Data_Parse)
    Pto_Data_Receive                         0x08001a61   Thumb Code    74  yb_protocol.o(i.Pto_Data_Receive)
    Pto_Loop                                 0x08001ab5   Thumb Code    46  yb_protocol.o(i.Pto_Loop)
    Rec_Data                                 0x08001aed   Thumb Code    50  uart_app.o(i.Rec_Data)
    SVC_Handler                              0x08001b29   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001b2b   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001b31   Thumb Code   140  main.o(i.SystemClock_Config)
    SystemInit                               0x08001bc5   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    UART_Start_Receive_IT                    0x08001e01   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08001ea9   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08001eb5   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x08001ec1   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    main                                     0x08001ee5   Thumb Code    38  main.o(i.main)
    _get_lc_ctype                            0x08001f15   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _fp_init                                 0x08001f41   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001f49   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001f49   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x08001f4a   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08001f5a   Data           8  system_stm32f4xx.o(.constdata)
    func_table                               0x08002024   Data         352  yb_protocol.o(.constdata)
    Region$$Table$$Base                      0x08002184   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080021a4   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080021b1   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    rec_data                                 0x20000010   Data           1  uart_app.o(.data)
    count                                    0x20000011   Data           1  uart_app.o(.data)
    rec_flag                                 0x20000012   Data           1  uart_app.o(.data)
    RxIndex                                  0x20000014   Data           1  yb_protocol.o(.data)
    RxFlag                                   0x20000015   Data           1  yb_protocol.o(.data)
    New_CMD_flag                             0x20000016   Data           1  yb_protocol.o(.data)
    New_CMD_length                           0x20000017   Data           1  yb_protocol.o(.data)
    lost_count                               0x20000018   Data           4  yb_protocol.o(.data)
    Lost_Flag                                0x2000001c   Data           4  yb_protocol.o(.data)
    Car_Auto_Drive                           0x20000020   Data           4  yb_protocol.o(.data)
    huart1                                   0x20000024   Data          72  usart.o(.bss)
    huart2                                   0x2000006c   Data          72  usart.o(.bss)
    rec_buff                                 0x200000b4   Data          64  uart_app.o(.bss)
    send_buff                                0x200000f4   Data         200  uart_app.o(.bss)
    RxBuffer                                 0x200001bc   Data          50  yb_protocol.o(.bss)
    __libspace_start                         0x200001f0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000250   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000022d8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000022b4, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         2229  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         2428    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         2430    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         2432    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         2214    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         2213    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000202   0x08000202   0x00000004   Code   RO         2247    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000206   0x08000206   0x00000002   Code   RO         2301    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000208   0x08000208   0x00000004   Code   RO         2310    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         2313    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         2316    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         2318    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         2320    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000006   Code   RO         2321    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2323    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x0000000c   Code   RO         2324    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2325    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2327    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2329    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2331    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2333    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2335    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2337    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2339    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2341    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2343    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2347    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2349    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2351    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2353    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000002   Code   RO         2354    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000220   0x08000220   0x00000002   Code   RO         2385    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2411    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2413    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2416    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2419    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2421    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         2424    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000222   0x08000222   0x00000002   Code   RO         2425    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         2231    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000224   0x08000224   0x00000000   Code   RO         2256    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000224   0x08000224   0x00000006   Code   RO         2268    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         2258    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800022a   0x0800022a   0x00000004   Code   RO         2259    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         2261    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000008   Code   RO         2262    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000236   0x08000236   0x00000002   Code   RO         2302    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000238   0x08000238   0x00000000   Code   RO         2358    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000238   0x08000238   0x00000004   Code   RO         2359    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         2360    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000284   0x08000284   0x000000ee   Code   RO         2183    .text               c_w.l(lludivv7m.o)
    0x08000372   0x08000372   0x00000002   PAD
    0x08000374   0x08000374   0x00000028   Code   RO         2187    .text               c_w.l(noretval__2sprintf.o)
    0x0800039c   0x0800039c   0x00000068   Code   RO         2189    .text               c_w.l(__printf.o)
    0x08000404   0x08000404   0x00000052   Code   RO         2191    .text               c_w.l(_printf_str.o)
    0x08000456   0x08000456   0x0000001a   Code   RO         2215    .text               c_w.l(atoi.o)
    0x08000470   0x08000470   0x0000003e   Code   RO         2217    .text               c_w.l(strlen.o)
    0x080004ae   0x080004ae   0x0000008a   Code   RO         2219    .text               c_w.l(rt_memcpy_v6.o)
    0x08000538   0x08000538   0x00000044   Code   RO         2221    .text               c_w.l(rt_memclr.o)
    0x0800057c   0x0800057c   0x0000004e   Code   RO         2223    .text               c_w.l(rt_memclr_w.o)
    0x080005ca   0x080005ca   0x00000002   PAD
    0x080005cc   0x080005cc   0x00000080   Code   RO         2225    .text               c_w.l(strcmpv7m.o)
    0x0800064c   0x0800064c   0x00000006   Code   RO         2227    .text               c_w.l(heapauxi.o)
    0x08000652   0x08000652   0x00000002   PAD
    0x08000654   0x08000654   0x00000008   Code   RO         2239    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800065c   0x0800065c   0x00000030   Code   RO         2241    .text               c_w.l(_printf_char_common.o)
    0x0800068c   0x0800068c   0x0000000a   Code   RO         2243    .text               c_w.l(_sputc.o)
    0x08000696   0x08000696   0x0000002c   Code   RO         2245    .text               c_w.l(_printf_char.o)
    0x080006c2   0x080006c2   0x00000070   Code   RO         2248    .text               c_w.l(strtol.o)
    0x08000732   0x08000732   0x00000064   Code   RO         2250    .text               c_w.l(rt_memcpy_w.o)
    0x08000796   0x08000796   0x00000002   PAD
    0x08000798   0x08000798   0x00000008   Code   RO         2252    .text               c_w.l(libspace.o)
    0x080007a0   0x080007a0   0x00000010   Code   RO         2270    .text               c_w.l(rt_ctype_table.o)
    0x080007b0   0x080007b0   0x0000009e   Code   RO         2276    .text               c_w.l(_strtoul.o)
    0x0800084e   0x0800084e   0x0000004a   Code   RO         2278    .text               c_w.l(sys_stackheap_outer.o)
    0x08000898   0x08000898   0x00000008   Code   RO         2283    .text               c_w.l(rt_locale_intlibspace.o)
    0x080008a0   0x080008a0   0x0000001c   Code   RO         2285    .text               c_w.l(_chval.o)
    0x080008bc   0x080008bc   0x00000012   Code   RO         2290    .text               c_w.l(exit.o)
    0x080008ce   0x080008ce   0x00000002   PAD
    0x080008d0   0x080008d0   0x0000000c   Code   RO         2355    .text               c_w.l(sys_exit.o)
    0x080008dc   0x080008dc   0x00000002   Code   RO         2374    .text               c_w.l(use_no_semi.o)
    0x080008de   0x080008de   0x00000000   Code   RO         2376    .text               c_w.l(indicate_semi.o)
    0x080008de   0x080008de   0x00000002   Code   RO          243    i.BusFault_Handler  stm32f4xx_it.o
    0x080008e0   0x080008e0   0x00000002   Code   RO          244    i.DebugMon_Handler  stm32f4xx_it.o
    0x080008e2   0x080008e2   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080008e6   0x080008e6   0x00000002   PAD
    0x080008e8   0x080008e8   0x00000028   Code   RO         1951    i.Get_LOST_Flag     yb_protocol.o
    0x08000910   0x08000910   0x00000092   Code   RO         1180    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080009a2   0x080009a2   0x00000024   Code   RO         1181    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080009c6   0x080009c6   0x00000002   PAD
    0x080009c8   0x080009c8   0x000001f0   Code   RO         1073    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08000bb8   0x08000bb8   0x0000000c   Code   RO         1623    i.HAL_GetTick       stm32f4xx_hal.o
    0x08000bc4   0x08000bc4   0x00000010   Code   RO         1629    i.HAL_IncTick       stm32f4xx_hal.o
    0x08000bd4   0x08000bd4   0x00000034   Code   RO         1630    i.HAL_Init          stm32f4xx_hal.o
    0x08000c08   0x08000c08   0x00000040   Code   RO         1631    i.HAL_InitTick      stm32f4xx_hal.o
    0x08000c48   0x08000c48   0x00000030   Code   RO          331    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08000c78   0x08000c78   0x0000001a   Code   RO         1465    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08000c92   0x08000c92   0x00000002   PAD
    0x08000c94   0x08000c94   0x00000040   Code   RO         1471    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08000cd4   0x08000cd4   0x00000024   Code   RO         1472    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08000cf8   0x08000cf8   0x00000134   Code   RO          719    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08000e2c   0x08000e2c   0x00000020   Code   RO          726    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08000e4c   0x08000e4c   0x00000020   Code   RO          727    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08000e6c   0x08000e6c   0x00000064   Code   RO          728    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08000ed0   0x08000ed0   0x0000036c   Code   RO          731    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800123c   0x0800123c   0x00000028   Code   RO         1476    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001264   0x08001264   0x00000002   Code   RO          367    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08001266   0x08001266   0x00000002   Code   RO          381    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08001268   0x08001268   0x00000280   Code   RO          384    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080014e8   0x080014e8   0x00000064   Code   RO          385    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x0800154c   0x0800154c   0x000000c8   Code   RO          196    i.HAL_UART_MspInit  usart.o
    0x08001614   0x08001614   0x0000001c   Code   RO          390    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08001630   0x08001630   0x00000034   Code   RO         1906    i.HAL_UART_RxCpltCallback  uart_app.o
    0x08001664   0x08001664   0x000000a0   Code   RO          393    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08001704   0x08001704   0x00000002   Code   RO          396    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08001706   0x08001706   0x00000002   Code   RO         1952    i.HandleAprilTag    yb_protocol.o
    0x08001708   0x08001708   0x00000002   Code   RO         1953    i.HandleBarcode     yb_protocol.o
    0x0800170a   0x0800170a   0x00000002   Code   RO         1954    i.HandleColor       yb_protocol.o
    0x0800170c   0x0800170c   0x00000002   Code   RO         1955    i.HandleDMCode      yb_protocol.o
    0x0800170e   0x0800170e   0x00000002   Code   RO         1956    i.HandleEyeGaze     yb_protocol.o
    0x08001710   0x08001710   0x00000002   Code   RO         1957    i.HandleFaceDetect  yb_protocol.o
    0x08001712   0x08001712   0x00000002   Code   RO         1958    i.HandleFaceRecognition  yb_protocol.o
    0x08001714   0x08001714   0x00000002   Code   RO         1959    i.HandleFallDown    yb_protocol.o
    0x08001716   0x08001716   0x00000002   Code   RO         1960    i.HandleGarbageDetect  yb_protocol.o
    0x08001718   0x08001718   0x00000002   Code   RO         1961    i.HandleGuideDetect  yb_protocol.o
    0x0800171a   0x0800171a   0x00000002   Code   RO         1962    i.HandleHandDetect  yb_protocol.o
    0x0800171c   0x0800171c   0x00000002   Code   RO         1963    i.HandleHandGesture  yb_protocol.o
    0x0800171e   0x0800171e   0x00000002   Code   RO         1964    i.HandleLicenceDetect  yb_protocol.o
    0x08001720   0x08001720   0x00000002   Code   RO         1965    i.HandleLicenceRec  yb_protocol.o
    0x08001722   0x08001722   0x00000002   PAD
    0x08001724   0x08001724   0x00000064   Code   RO         1966    i.HandleMultiColorRec  yb_protocol.o
    0x08001788   0x08001788   0x00000002   Code   RO         1967    i.HandleNanoTracker  yb_protocol.o
    0x0800178a   0x0800178a   0x00000006   Code   RO         1968    i.HandleOCRRec      yb_protocol.o
    0x08001790   0x08001790   0x00000002   Code   RO         1969    i.HandleObjectDetect  yb_protocol.o
    0x08001792   0x08001792   0x00000002   Code   RO         1970    i.HandleObstacleDetect  yb_protocol.o
    0x08001794   0x08001794   0x00000002   Code   RO         1971    i.HandlePersonDetect  yb_protocol.o
    0x08001796   0x08001796   0x00000002   Code   RO         1972    i.HandleQRCode      yb_protocol.o
    0x08001798   0x08001798   0x00000002   Code   RO         1973    i.HandleSelfLearning  yb_protocol.o
    0x0800179a   0x0800179a   0x00000002   Code   RO          245    i.HardFault_Handler  stm32f4xx_it.o
    0x0800179c   0x0800179c   0x0000002c   Code   RO          171    i.MX_GPIO_Init      gpio.o
    0x080017c8   0x080017c8   0x00000038   Code   RO          197    i.MX_USART1_UART_Init  usart.o
    0x08001800   0x08001800   0x00000038   Code   RO          198    i.MX_USART2_UART_Init  usart.o
    0x08001838   0x08001838   0x00000002   Code   RO          246    i.MemManage_Handler  stm32f4xx_it.o
    0x0800183a   0x0800183a   0x00000002   Code   RO          247    i.NMI_Handler       stm32f4xx_it.o
    0x0800183c   0x0800183c   0x000000d8   Code   RO         1907    i.OCRrec_Actions    uart_app.o
    0x08001914   0x08001914   0x00000094   Code   RO         1974    i.ParseCommonFields  yb_protocol.o
    0x080019a8   0x080019a8   0x00000002   Code   RO          248    i.PendSV_Handler    stm32f4xx_it.o
    0x080019aa   0x080019aa   0x00000004   Code   RO         1975    i.Pto_Char_To_Int   yb_protocol.o
    0x080019ae   0x080019ae   0x00000002   PAD
    0x080019b0   0x080019b0   0x00000020   Code   RO         1976    i.Pto_Clear_CMD_Flag  yb_protocol.o
    0x080019d0   0x080019d0   0x0000008e   Code   RO         1977    i.Pto_Data_Parse    yb_protocol.o
    0x08001a5e   0x08001a5e   0x00000002   PAD
    0x08001a60   0x08001a60   0x00000054   Code   RO         1978    i.Pto_Data_Receive  yb_protocol.o
    0x08001ab4   0x08001ab4   0x00000038   Code   RO         1979    i.Pto_Loop          yb_protocol.o
    0x08001aec   0x08001aec   0x0000003c   Code   RO         1908    i.Rec_Data          uart_app.o
    0x08001b28   0x08001b28   0x00000002   Code   RO          249    i.SVC_Handler       stm32f4xx_it.o
    0x08001b2a   0x08001b2a   0x00000004   Code   RO          250    i.SysTick_Handler   stm32f4xx_it.o
    0x08001b2e   0x08001b2e   0x00000002   PAD
    0x08001b30   0x08001b30   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08001bc4   0x08001bc4   0x00000010   Code   RO         1869    i.SystemInit        system_stm32f4xx.o
    0x08001bd4   0x08001bd4   0x0000000e   Code   RO          398    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08001be2   0x08001be2   0x0000004e   Code   RO          408    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08001c30   0x08001c30   0x000000c2   Code   RO          410    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08001cf2   0x08001cf2   0x00000002   PAD
    0x08001cf4   0x08001cf4   0x0000010c   Code   RO          411    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08001e00   0x08001e00   0x00000036   Code   RO          413    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08001e36   0x08001e36   0x00000072   Code   RO          414    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08001ea8   0x08001ea8   0x0000000c   Code   RO          251    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08001eb4   0x08001eb4   0x0000000c   Code   RO          252    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08001ec0   0x08001ec0   0x00000002   Code   RO          253    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001ec2   0x08001ec2   0x00000020   Code   RO         1478    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001ee2   0x08001ee2   0x00000002   PAD
    0x08001ee4   0x08001ee4   0x00000030   Code   RO           15    i.main              main.o
    0x08001f14   0x08001f14   0x0000002c   Code   RO         2288    locale$$code        c_w.l(lc_ctype_c.o)
    0x08001f40   0x08001f40   0x0000000a   Code   RO         2370    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001f4a   0x08001f4a   0x00000010   Data   RO         1870    .constdata          system_stm32f4xx.o
    0x08001f5a   0x08001f5a   0x00000008   Data   RO         1871    .constdata          system_stm32f4xx.o
    0x08001f62   0x08001f62   0x00000008   Data   RO         1981    .constdata          yb_protocol.o
    0x08001f6a   0x08001f6a   0x0000000a   Data   RO         1982    .constdata          yb_protocol.o
    0x08001f74   0x08001f74   0x0000000a   Data   RO         1983    .constdata          yb_protocol.o
    0x08001f7e   0x08001f7e   0x0000000c   Data   RO         1984    .constdata          yb_protocol.o
    0x08001f8a   0x08001f8a   0x0000000c   Data   RO         1985    .constdata          yb_protocol.o
    0x08001f96   0x08001f96   0x00000008   Data   RO         1986    .constdata          yb_protocol.o
    0x08001f9e   0x08001f9e   0x00000008   Data   RO         1987    .constdata          yb_protocol.o
    0x08001fa6   0x08001fa6   0x0000000c   Data   RO         1988    .constdata          yb_protocol.o
    0x08001fb2   0x08001fb2   0x00000008   Data   RO         1989    .constdata          yb_protocol.o
    0x08001fba   0x08001fba   0x0000000c   Data   RO         1990    .constdata          yb_protocol.o
    0x08001fc6   0x08001fc6   0x00000008   Data   RO         1991    .constdata          yb_protocol.o
    0x08001fce   0x08001fce   0x00000002   Data   RO         1992    .constdata          yb_protocol.o
    0x08001fd0   0x08001fd0   0x00000002   Data   RO         1993    .constdata          yb_protocol.o
    0x08001fd2   0x08001fd2   0x0000000a   Data   RO         1994    .constdata          yb_protocol.o
    0x08001fdc   0x08001fdc   0x00000008   Data   RO         1995    .constdata          yb_protocol.o
    0x08001fe4   0x08001fe4   0x00000004   Data   RO         1996    .constdata          yb_protocol.o
    0x08001fe8   0x08001fe8   0x00000002   Data   RO         1997    .constdata          yb_protocol.o
    0x08001fea   0x08001fea   0x00000010   Data   RO         1998    .constdata          yb_protocol.o
    0x08001ffa   0x08001ffa   0x0000000a   Data   RO         1999    .constdata          yb_protocol.o
    0x08002004   0x08002004   0x0000000a   Data   RO         2000    .constdata          yb_protocol.o
    0x0800200e   0x0800200e   0x0000000a   Data   RO         2001    .constdata          yb_protocol.o
    0x08002018   0x08002018   0x0000000a   Data   RO         2002    .constdata          yb_protocol.o
    0x08002022   0x08002022   0x00000002   PAD
    0x08002024   0x08002024   0x00000160   Data   RO         2003    .constdata          yb_protocol.o
    0x08002184   0x08002184   0x00000020   Data   RO         2426    Region$$Table       anon$$obj.o
    0x080021a4   0x080021a4   0x00000110   Data   RO         2287    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080022b4, Size: 0x00000850, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080022b4   0x0000000c   Data   RW         1637    .data               stm32f4xx_hal.o
    0x2000000c   0x080022c0   0x00000004   Data   RW         1872    .data               system_stm32f4xx.o
    0x20000010   0x080022c4   0x00000003   Data   RW         1912    .data               uart_app.o
    0x20000013   0x080022c7   0x00000001   PAD
    0x20000014   0x080022c8   0x00000010   Data   RW         2004    .data               yb_protocol.o
    0x20000024        -       0x00000090   Zero   RW          199    .bss                usart.o
    0x200000b4        -       0x00000108   Zero   RW         1909    .bss                uart_app.o
    0x200001bc        -       0x00000032   Zero   RW         1980    .bss                yb_protocol.o
    0x200001ee   0x080022d8   0x00000002   PAD
    0x200001f0        -       0x00000060   Zero   RW         2253    .bss                c_w.l(libspace.o)
    0x20000250        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000450        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080022d8, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          6          0          0          0        755   gpio.o
       200         18          0          0          0     659476   main.o
        64         26        392          0       1536        824   startup_stm32f407xx.o
       144         24          0         12          0       8625   stm32f4xx_hal.o
       198         14          0          0          0      33715   stm32f4xx_hal_cortex.o
       182          0          0          0          0       2039   stm32f4xx_hal_dma.o
       496         46          0          0          0       1492   stm32f4xx_hal_gpio.o
        48          6          0          0          0        818   stm32f4xx_hal_msp.o
      1348         76          0          0          0       5132   stm32f4xx_hal_rcc.o
      1656         14          0          0          0      11144   stm32f4xx_hal_uart.o
        44         12          0          0          0       4870   stm32f4xx_it.o
        16          4         24          4          0       1067   system_stm32f4xx.o
       328        134          0          3        264       2469   uart_app.o
       312         34          0          0        144       2335   usart.o
       652         68        544         16         50      19749   yb_protocol.o

    ----------------------------------------------------------------------
      5750        <USER>        <GROUP>         36       1996     754510   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          2          1          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
         2          0          0          0          0          0   libinit.o
        24          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      1868         <USER>        <GROUP>          0         96       2376   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1844         50        272          0         96       2260   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      1868         <USER>        <GROUP>          0         96       2376   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7618        532       1266         36       2092     748138   Grand Totals
      7618        532       1266         36       2092     748138   ELF Image Totals
      7618        532       1266         36          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8884 (   8.68kB)
    Total RW  Size (RW Data + ZI Data)              2128 (   2.08kB)
    Total ROM Size (Code + RO Data + RW Data)       8920 (   8.71kB)

==============================================================================

